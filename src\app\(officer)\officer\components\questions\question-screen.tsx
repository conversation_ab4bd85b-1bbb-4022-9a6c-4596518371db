import Button from "@/components/global/buttons/button";
import { VStack, Image, HStack, Text, Box, Flex } from "@chakra-ui/react";
import { FaPlay } from "react-icons/fa";
import { IoIosTimer } from "react-icons/io";
import EssayQuestion from "./essay-question";
import ObjectiveQuestion from "./objective-question";
import { ApiQuestionsDTO } from "@/utils/types/DTO/questions.dto";
import { useEffect, useState } from "react";
import { formatTime } from "@/utils/funcs/format-timer";

type QuestionScreenProps = {
  questionGroup: ApiQuestionsDTO["questionsGroups"][0];
  questionGroupsTotal: number;
  questionGroupIndex: number;
  onConfirm: () => void;
  onWatchVideoAgain: () => void;
  elapsedTime?: number;
};

export default function QuestionScreen({
  questionGroup,
  questionGroupIndex = 0,
  questionGroupsTotal = 30,
  onConfirm,
  onWatchVideoAgain,
  elapsedTime = 0,
}: QuestionScreenProps) {
  const [type, setType] = useState<"essay" | "objective">("essay");
  const [answers, setAnswers] =
    useState<{ answer: string; type: "essay" | "objective" }[]>();
  const [isReadyToSave, setIsReadyToSave] = useState(false);

  useEffect(() => {
    const verifyHasSavedAnswers = () => {
      const savedAnswerers = localStorage.getItem("assmt_answers");
      if (!savedAnswerers) {
        return;
      }
      const parsedAnswers = JSON.parse(savedAnswerers);
      if (parsedAnswers[questionGroup.secureId]) {
        setAnswers(
          parsedAnswers[questionGroup.secureId].map((answer: any) => ({
            answer: answer.answer,
            type: answer.type,
          }))
        );
        setType("essay");
      }
    };
    verifyHasSavedAnswers();
  }, [questionGroup]);

  const handleSaveAnswer = () => {
    if (!answers) {
      return;
    }
    const savedAnswerers = localStorage.getItem("assmt_answers");
    if (!savedAnswerers) {
      localStorage.setItem(
        "assmt_answers",
        JSON.stringify({
          [questionGroup.secureId]: answers?.map((answer) => ({
            answer: answer.answer,
            type: answer.type,
          })),
        })
      );
      onConfirm();
      setAnswers([]);
      setType("essay");
      setIsReadyToSave(false);
      return;
    }
    const parsedAnswers = JSON.parse(savedAnswerers);
    (parsedAnswers[questionGroup.secureId] = answers.map((answer) => ({
      answer: answer.answer,
      type: answer.type,
    }))),
      localStorage.setItem("assmt_answers", JSON.stringify(parsedAnswers));
    onConfirm();
    setAnswers([]);
    setType("essay");
    setIsReadyToSave(false);
  };

  const handleChangeQuestion = (value: string, type: "essay" | "objective") => {
    setAnswers((prevAnswers) => {
      if (!prevAnswers) {
        return [{ answer: value, type }];
      }
      const existingAnswerIndex = prevAnswers.findIndex(
        (answer) => answer.type === type
      );
      if (existingAnswerIndex !== -1) {
        const updatedAnswers = [...prevAnswers];
        updatedAnswers[existingAnswerIndex] = { answer: value, type };
        return updatedAnswers;
      }
      return [...prevAnswers, { answer: value, type }];
    });
    console.log("Updated answers:", answers);
    if (type === "essay") {
      setType("objective");
      return;
    }
    if (type === "objective") {
      setIsReadyToSave(true);
    }
    return;
  };

  useEffect(() => {
    if (answers && answers.length > 1 && isReadyToSave) {
      console.log("Saving answers:", answers);
      handleSaveAnswer();
    }
  }, [answers, isReadyToSave]);

  return (
    <VStack
      flex={1}
      width={"100vw"}
      height={"100vh"}
      position="relative"
      px={20}
      py={{
        base: 5,
        "2xl": 10,
      }}
    >
      <HStack
        w={"100%"}
        justifyContent="space-between"
        mb={{
          base: 4,
          "2xl": 8,
        }}
      >
        <HStack gap={16} alignItems="center">
          <Image
            src="/images/logoBancoABC.svg"
            alt="Banco ABC"
            w={{ base: "60px", "2xl": "100px" }}
            h="auto"
            filter={
              "brightness(0) saturate(100%) invert(11%) sepia(4%) saturate(0%) hue-rotate(314deg) brightness(96%) contrast(88%)"
            }
          />
          <Text
            fontSize={{ base: 20, "2xl": 24 }}
            fontWeight={"bold"}
            color="black"
          >
            Questão {questionGroupIndex + 1}/{questionGroupsTotal}
          </Text>
          <Button
            pl={0}
            borderRadius={8}
            bgColor={"rgb(32,32,31)"}
            onClick={onWatchVideoAgain}
          >
            <Flex alignItems="center" justifyContent={"space-between"} gap={2}>
              <Box
                bgColor={"rgb(212,212,212)"}
                color={"black"}
                p={2}
                borderRadius={8}
              >
                <FaPlay />
              </Box>
              <Text fontSize={"md"} color={"white"} fontWeight={"bold"}>
                Assistir video novamente
              </Text>
            </Flex>
          </Button>
        </HStack>
        <HStack
          color={"rgb(32,32,31)"}
          fontSize={{ base: 20, "2xl": 24 }}
          gap={3}
          alignItems="center"
          justifyContent="center"
        >
          {formatTime(elapsedTime)}
          <IoIosTimer size={40} />
        </HStack>
      </HStack>
      {type === "essay" ? (
        <EssayQuestion
          handleChangeForObjective={handleChangeQuestion}
          savedValue={
            answers?.find((as) => {
              return as.type === "essay";
            })?.answer
          }
          questionStatement={questionGroup.essayQuestion}
        />
      ) : (
        <ObjectiveQuestion
          handleSaveAnswer={handleChangeQuestion}
          options={questionGroup.objectiveQuestionChoices}
          savedValue={
            answers?.find((as) => {
              return as.type === "objective";
            })?.answer
          }
          questionStatement={questionGroup.objectiveQuestion}
        />
      )}
    </VStack>
  );
}
