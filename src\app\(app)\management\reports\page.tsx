"use client";
import {
  Box,
  Checkbox,
  CheckboxCard,
  Flex,
  HStack,
  Image,
  Input,
  InputGroup,
  Text,
  VStack,
} from "@chakra-ui/react";
import { LuSearch } from "react-icons/lu";

const data = {
  data: [
    {
      secureId: "702bb56d-6c80-48d7-885a-c8ace463d15b",
      segmento_nome: "Corporate",
      superintendencias: [
        {
          secureId: "b82f4fc7-318b-42eb-a881-655359001f4a",
          superintendencia_nome: "SuperIntendência A Corp",
          gerencias: [
            {
              secureId: "89973493-eae0-43be-bd2b-1d39c124c37e",
              gerencia_nome: "Gerente A",
              cargos: [
                {
                  secureId: "c16ebad8-4026-41bd-a0b5-9d94593e6060",
                  cargo_nome: "Officer Jr",
                  createdAt: "2025-06-11T19:24:25.568Z",
                  updatedAt: "2025-06-11T19:24:25.568Z",
                },
                {
                  secureId: "0160279a-d612-4fcb-bdb4-a5be868a12d0",
                  cargo_nome: "Officer Pl",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "354496f9-b467-4b3f-ba03-31ca8ce0bcd4",
                  cargo_nome: "Officer Sr",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "99135df3-b40f-4f85-87dc-c4cdf25584c0",
                  cargo_nome: "Officer Esp",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
              ],
            },
            {
              secureId: "f8f7d0a1-6cb3-4f84-b701-15d78fd7d5ec",
              gerencia_nome: "Gerente B",
              cargos: [
                {
                  secureId: "c16ebad8-4026-41bd-a0b5-9d94593e6060",
                  cargo_nome: "Officer Jr",
                  createdAt: "2025-06-11T19:24:25.568Z",
                  updatedAt: "2025-06-11T19:24:25.568Z",
                },
                {
                  secureId: "0160279a-d612-4fcb-bdb4-a5be868a12d0",
                  cargo_nome: "Officer Pl",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "354496f9-b467-4b3f-ba03-31ca8ce0bcd4",
                  cargo_nome: "Officer Sr",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "99135df3-b40f-4f85-87dc-c4cdf25584c0",
                  cargo_nome: "Officer Esp",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
              ],
            },
          ],
        },
        {
          secureId: "ec4345dc-eed1-43fa-8f4f-ec2b100376be",
          superintendencia_nome: "SuperIntendência B Corp",
          gerencias: [
            {
              secureId: "f8f7d0a1-6cb3-4f84-b701-15d78fd7d5ec",
              gerencia_nome: "Gerente B",
              cargos: [
                {
                  secureId: "c16ebad8-4026-41bd-a0b5-9d94593e6060",
                  cargo_nome: "Officer Jr",
                  createdAt: "2025-06-11T19:24:25.568Z",
                  updatedAt: "2025-06-11T19:24:25.568Z",
                },
                {
                  secureId: "0160279a-d612-4fcb-bdb4-a5be868a12d0",
                  cargo_nome: "Officer Pl",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "354496f9-b467-4b3f-ba03-31ca8ce0bcd4",
                  cargo_nome: "Officer Sr",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "99135df3-b40f-4f85-87dc-c4cdf25584c0",
                  cargo_nome: "Officer Esp",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
              ],
            },
          ],
        },
      ],
    },
    {
      secureId: "9f16a04a-f0cd-417e-bbe4-6a5e51ee6fde",
      segmento_nome: "Middle",
      superintendencias: [
        {
          secureId: "b82f4fc7-318b-42eb-a881-655359001f4a",
          superintendencia_nome: "SuperIntendência A Middle",
          gerencias: [
            {
              secureId: "64ae3d96-2c0e-4956-b204-ed576841805c",
              gerencia_nome: "Gerente C",
              cargos: [
                {
                  secureId: "1b2ef5b0-9239-45e4-84df-988a7a506b61",
                  cargo_nome: "Officer Ex",
                  createdAt: "2025-06-11T19:24:25.568Z",
                  updatedAt: "2025-06-11T19:24:25.568Z",
                },
                {
                  secureId: "fb296459-ff63-418d-9b07-15285c2e7d88",
                  cargo_nome: "Officer Kc",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};

export default function Reports() {
  return (
    <Flex flex={1} position={"relative"} overflowX={"auto"}>
      <VStack w={"100%"}>
        <HStack
          w={"100%"}
          justifyContent="space-between"
          alignItems="space-between"
          gap={8}
        >
          <HStack
            w={"100%"}
            justifyContent="center"
            alignItems="center"
            gap={8}
          >
            <Image
              src="/images/logoBancoABC.svg"
              alt="Banco ABC"
              w="100px"
              h="auto"
            />
            <Text fontSize={"40px"}>Filtro Visão Banco</Text>
          </HStack>
          <InputGroup startElement={<LuSearch />}>
            <Input
              placeholder="Buscar por nome respondente"
              bg="white"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
          </InputGroup>
        </HStack>
        <VStack gap={4} align={"stretch"}>
          <HStack>
            <CheckboxCard.Root
              borderRadius={"lg"}
              bg={"white"}
              align={"center"}
              border={"none"}
            >
              <CheckboxCard.HiddenInput />
              <CheckboxCard.Control p={0} pr={2}>
                <CheckboxCard.Indicator
                  w={10}
                  h={"auto"}
                  bgColor={"#a6864a"}
                  borderLeftRadius={"lg"}
                  borderColor={"gray.600"}
                />
                <CheckboxCard.Label
                  color={"black"}
                  fontWeight={"bold"}
                  fontSize={"sm"}
                >
                  TODOS
                </CheckboxCard.Label>
              </CheckboxCard.Control>
            </CheckboxCard.Root>
            <Text fontSize={"2xl"}>Escolha o Segmento e Cargo</Text>
          </HStack>
          <VStack gap={4} align={"stretch"}>
            <Checkbox.Root>
              <Checkbox.HiddenInput />
              <Checkbox.Control
                w={8}
                h={8}
                borderRadius={"2xl"}
                bgColor={"gray.600"}
              />
              <Checkbox.Label fontSize={"xl"}>Rodada</Checkbox.Label>
            </Checkbox.Root>
          </VStack>
          <CheckboxCard.Root
            borderRadius={"full"}
            border={"1px solid"}
            borderColor={"gray.600"}
            align={"center"}
          >
            <CheckboxCard.HiddenInput />
            <CheckboxCard.Control p={0} pr={0}>
              <CheckboxCard.Indicator
                w={10}
                h={10}
                bgColor={"gray.600"}
                borderRadius={"2xl"}
                borderColor={"gray.600"}
              />
              <CheckboxCard.Label fontSize={"2xl"}>
                Corporate
              </CheckboxCard.Label>
            </CheckboxCard.Control>
          </CheckboxCard.Root>
        </VStack>
      </VStack>
    </Flex>
  );
}
