"use client";
import {
  Box,
  Checkbox,
  CheckboxCard,
  Flex,
  HStack,
  Image,
  Input,
  InputGroup,
  Text,
  VStack,
} from "@chakra-ui/react";
import { LuSearch } from "react-icons/lu";
import { useState, useCallback } from "react";

const data = {
  data: [
    {
      secureId: "702bb56d-6c80-48d7-885a-c8ace463d15b",
      segmento_nome: "Corporate",
      superintendencias: [
        {
          secureId: "b82f4fc7-318b-42eb-a881-655359001f4a",
          superintendencia_nome: "SuperIntendência A Corp",
          gerencias: [
            {
              secureId: "89973493-eae0-43be-bd2b-1d39c124c37e",
              gerencia_nome: "Gerente A",
              cargos: [
                {
                  secureId: "c16ebad8-4026-41bd-a0b5-9d94593e6060",
                  cargo_nome: "Officer Jr",
                  createdAt: "2025-06-11T19:24:25.568Z",
                  updatedAt: "2025-06-11T19:24:25.568Z",
                },
                {
                  secureId: "0160279a-d612-4fcb-bdb4-a5be868a12d0",
                  cargo_nome: "Officer Pl",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "354496f9-b467-4b3f-ba03-31ca8ce0bcd4",
                  cargo_nome: "Officer Sr",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "99135df3-b40f-4f85-87dc-c4cdf25584c0",
                  cargo_nome: "Officer Esp",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
              ],
            },
            {
              secureId: "f8f7d0a1-6cb3-4f84-b701-15d78fd7d5ec",
              gerencia_nome: "Gerente B",
              cargos: [
                {
                  secureId: "c16ebad8-4026-41bd-a0b5-9d94593e6060",
                  cargo_nome: "Officer Jr",
                  createdAt: "2025-06-11T19:24:25.568Z",
                  updatedAt: "2025-06-11T19:24:25.568Z",
                },
                {
                  secureId: "0160279a-d612-4fcb-bdb4-a5be868a12d0",
                  cargo_nome: "Officer Pl",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "354496f9-b467-4b3f-ba03-31ca8ce0bcd4",
                  cargo_nome: "Officer Sr",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "99135df3-b40f-4f85-87dc-c4cdf25584c0",
                  cargo_nome: "Officer Esp",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
              ],
            },
          ],
        },
        {
          secureId: "ec4345dc-eed1-43fa-8f4f-ec2b100376be",
          superintendencia_nome: "SuperIntendência B Corp",
          gerencias: [
            {
              secureId: "f8f7d0a1-6cb3-4f84-b701-15d78fd7d5ec",
              gerencia_nome: "Gerente B",
              cargos: [
                {
                  secureId: "c16ebad8-4026-41bd-a0b5-9d94593e6060",
                  cargo_nome: "Officer Jr",
                  createdAt: "2025-06-11T19:24:25.568Z",
                  updatedAt: "2025-06-11T19:24:25.568Z",
                },
                {
                  secureId: "0160279a-d612-4fcb-bdb4-a5be868a12d0",
                  cargo_nome: "Officer Pl",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "354496f9-b467-4b3f-ba03-31ca8ce0bcd4",
                  cargo_nome: "Officer Sr",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
                {
                  secureId: "99135df3-b40f-4f85-87dc-c4cdf25584c0",
                  cargo_nome: "Officer Esp",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
              ],
            },
          ],
        },
      ],
    },
    {
      secureId: "9f16a04a-f0cd-417e-bbe4-6a5e51ee6fde",
      segmento_nome: "Middle",
      superintendencias: [
        {
          secureId: "b82f4fc7-318b-42eb-a881-655357001f4a",
          superintendencia_nome: "SuperIntendência A Middle",
          gerencias: [
            {
              secureId: "64ae3d96-2c0e-4956-b204-ed576841805c",
              gerencia_nome: "Gerente C",
              cargos: [
                {
                  secureId: "1b2ef5b0-9239-45e4-84df-988a7a506b61",
                  cargo_nome: "Officer Ex",
                  createdAt: "2025-06-11T19:24:25.568Z",
                  updatedAt: "2025-06-11T19:24:25.568Z",
                },
                {
                  secureId: "fb296459-ff63-418d-9b07-15285c2e7d88",
                  cargo_nome: "Officer Kc",
                  createdAt: "2025-06-11T19:24:25.583Z",
                  updatedAt: "2025-06-11T19:24:25.583Z",
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};

// Types for the hierarchical data structure
interface Cargo {
  secureId: string;
  cargo_nome: string;
  createdAt: string;
  updatedAt: string;
}

interface Gerencia {
  secureId: string;
  gerencia_nome: string;
  cargos: Cargo[];
}

interface Superintendencia {
  secureId: string;
  superintendencia_nome: string;
  gerencias: Gerencia[];
}

interface Segment {
  secureId: string;
  segmento_nome: string;
  superintendencias: Superintendencia[];
}

// Component for individual CheckboxCard items
interface HierarchicalCheckboxCardProps {
  id: string;
  label: string;
  isChecked: boolean;
  onChange: (id: string, checked: boolean) => void;
  level: "segment" | "superintendencia" | "gerencia" | "cargo";
  children?: React.ReactNode;
}

function HierarchicalCheckboxCard({
  id,
  label,
  isChecked,
  onChange,
  level,
  children,
}: HierarchicalCheckboxCardProps) {
  const handleChange = (checked: boolean) => {
    onChange(id, checked);
  };

  const getFontSize = () => {
    switch (level) {
      case "segment":
        return "2xl";
      case "superintendencia":
        return "xl";
      case "gerencia":
        return "lg";
      case "cargo":
        return "md";
    }
  };

  const getMarginLeft = () => {
    switch (level) {
      case "segment":
        return 0;
      case "superintendencia":
        return 4;
      case "gerencia":
        return 8;
      case "cargo":
        return 12;
    }
  };

  const getIndicatorSize = () => {
    switch (level) {
      case "segment":
        return 10;
      default:
        return 8;
    }
  };

  return (
    <VStack align="stretch" gap={2}>
      <CheckboxCard.Root
        w={"cover"}
        variant={"surface"}
        borderRadius="full"
        border="1px solid"
        borderColor={isChecked ? "#a6864a" : "gray.600"}
        bg={"transparent"}
        align="center"
        ml={getMarginLeft()}
        mb={2}
        outline={"none"}
      >
        <CheckboxCard.HiddenInput
          checked={isChecked}
          onChange={(e) => handleChange(e.target.checked)}
        />
        <CheckboxCard.Control p={0} pr={0}>
          <CheckboxCard.Indicator
            w={getIndicatorSize()}
            h={getIndicatorSize()}
            bgColor={isChecked ? "#a6864a" : "gray.600"}
            color={"transparent"}
            borderRadius="2xl"
            borderColor={isChecked ? "#a6864a" : "gray.600"}
          />
          <CheckboxCard.Label fontSize={getFontSize()} color={"white"}>
            {label}
          </CheckboxCard.Label>
        </CheckboxCard.Control>
      </CheckboxCard.Root>
      {children && <Box>{children}</Box>}
    </VStack>
  );
}

export default function Reports() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [allSelected, setAllSelected] = useState(false);
  const [selectedRounds, setSelectedRounds] = useState<Set<string>>(new Set());

  // Get all possible IDs for hierarchy management
  const getAllChildIds = useCallback((segment: Segment): string[] => {
    const ids: string[] = [segment.secureId];
    segment.superintendencias.forEach((sup) => {
      ids.push(sup.secureId);
      sup.gerencias.forEach((ger) => {
        ids.push(ger.secureId);
        ger.cargos.forEach((cargo) => {
          ids.push(cargo.secureId);
        });
      });
    });
    return ids;
  }, []);

  const getAllIds = useCallback((): string[] => {
    const allIds: string[] = [];
    data.data.forEach((segment) => {
      allIds.push(...getAllChildIds(segment));
    });
    return allIds;
  }, [getAllChildIds]);

  // Handle hierarchical selection
  const handleItemChange = useCallback(
    (id: string, checked: boolean) => {
      const newSelected = new Set(selectedItems);

      if (checked) {
        newSelected.add(id);
        // Add all children
        data.data.forEach((segment) => {
          if (segment.secureId === id) {
            getAllChildIds(segment).forEach((childId) =>
              newSelected.add(childId)
            );
          } else {
            segment.superintendencias.forEach((sup) => {
              if (sup.secureId === id) {
                newSelected.add(sup.secureId);
                sup.gerencias.forEach((ger) => {
                  newSelected.add(ger.secureId);
                  ger.cargos.forEach((cargo) =>
                    newSelected.add(cargo.secureId)
                  );
                });
              } else {
                sup.gerencias.forEach((ger) => {
                  if (ger.secureId === id) {
                    newSelected.add(ger.secureId);
                    ger.cargos.forEach((cargo) =>
                      newSelected.add(cargo.secureId)
                    );
                  }
                });
              }
            });
          }
        });
      } else {
        newSelected.delete(id);
        // Remove all children
        data.data.forEach((segment) => {
          if (segment.secureId === id) {
            getAllChildIds(segment).forEach((childId) =>
              newSelected.delete(childId)
            );
          } else {
            segment.superintendencias.forEach((sup) => {
              if (sup.secureId === id) {
                newSelected.delete(sup.secureId);
                sup.gerencias.forEach((ger) => {
                  newSelected.delete(ger.secureId);
                  ger.cargos.forEach((cargo) =>
                    newSelected.delete(cargo.secureId)
                  );
                });
              } else {
                sup.gerencias.forEach((ger) => {
                  if (ger.secureId === id) {
                    newSelected.delete(ger.secureId);
                    ger.cargos.forEach((cargo) =>
                      newSelected.delete(cargo.secureId)
                    );
                  }
                });
              }
            });
          }
        });
      }

      setSelectedItems(newSelected);
      setAllSelected(false);
    },
    [selectedItems, getAllChildIds]
  );

  // Handle "TODOS" selection
  const handleAllChange = useCallback(
    (checked: boolean) => {
      setAllSelected(checked);
      if (checked) {
        setSelectedItems(new Set(getAllIds()));
      } else {
        setSelectedItems(new Set());
      }
    },
    [getAllIds]
  );

  // Handle round selection
  const handleRoundChange = useCallback(
    (round: string, checked: boolean) => {
      const newRounds = new Set(selectedRounds);
      if (checked) {
        newRounds.add(round);
        // If "Rodada" is selected, select both Rodada 1 and Rodada 2
        if (round === "rodada") {
          newRounds.add("rodada1");
          newRounds.add("rodada2");
        }
      } else {
        newRounds.delete(round);
        // If "Rodada" is deselected, deselect both Rodada 1 and Rodada 2
        if (round === "rodada") {
          newRounds.delete("rodada1");
          newRounds.delete("rodada2");
        }
      }
      setSelectedRounds(newRounds);
    },
    [selectedRounds]
  );

  return (
    <Flex flex={1} position="relative" overflowX="auto" p={6}>
      <VStack w="100%" gap={6} align="stretch">
        {/* Header */}
        <HStack
          w="100%"
          justifyContent="space-between"
          alignItems="center"
          mb={8}
        >
          <HStack gap={32}>
            <Image
              src="/images/logoBancoABC.svg"
              alt="Banco ABC"
              w="100px"
              h="auto"
            />
            <Text fontSize="40px">Filtro Visão Banco</Text>
          </HStack>
          <InputGroup startElement={<LuSearch />} maxW="400px">
            <Input
              placeholder="Buscar por nome respondente"
              bg="white"
              border="1px solid"
              borderColor="gray.600"
              color="black"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
          </InputGroup>
        </HStack>

        {/* Main Content */}
        <HStack align="flex-start" gap={8}>
          {/* Left Sidebar - Controls */}
          <VStack gap={16} align="stretch" minW="250px">
            {/* TODOS Checkbox - mantendo estilo original */}
            <CheckboxCard.Root
              borderRadius="lg"
              bg={"white"}
              align="center"
              w={"fit-content"}
              border="none"
            >
              <CheckboxCard.HiddenInput
                checked={allSelected}
                onChange={(e) => handleAllChange(e.target.checked)}
              />
              <CheckboxCard.Control p={0} pr={2}>
                <CheckboxCard.Indicator
                  w={10}
                  h="auto"
                  bgColor="#a6864a"
                  borderLeftRadius="lg"
                  borderColor="gray.600"
                />
                <CheckboxCard.Label
                  color="black"
                  fontWeight="bold"
                  fontSize="sm"
                >
                  TODOS
                </CheckboxCard.Label>
              </CheckboxCard.Control>
            </CheckboxCard.Root>

            {/* Rounds Checkboxes - mantendo estilo original */}
            <VStack gap={4} align="stretch">
              <Checkbox.Root
                variant={"subtle"}
                checked={selectedRounds.has("rodada")}
                onCheckedChange={(checked) =>
                  handleRoundChange("rodada", !!checked.checked)
                }
              >
                <Checkbox.HiddenInput />
                <Checkbox.Control
                  w={8}
                  h={8}
                  borderRadius="2xl"
                  color={"transparent"}
                  bgColor={
                    selectedRounds.has("rodada") ? "#a6864a" : "gray.600"
                  }
                />
                <Checkbox.Label fontSize="xl">Rodada</Checkbox.Label>
              </Checkbox.Root>

              <Checkbox.Root
                variant={"subtle"}
                checked={selectedRounds.has("rodada1")}
                onCheckedChange={(checked) =>
                  handleRoundChange("rodada1", !!checked.checked)
                }
              >
                <Checkbox.HiddenInput />
                <Checkbox.Control
                  w={8}
                  h={8}
                  borderRadius="2xl"
                  color={"transparent"}
                  bgColor={
                    selectedRounds.has("rodada1") ? "#a6864a" : "gray.600"
                  }
                />
                <Checkbox.Label fontSize="xl">Rodada 1</Checkbox.Label>
              </Checkbox.Root>

              <Checkbox.Root
                variant={"subtle"}
                checked={selectedRounds.has("rodada2")}
                onCheckedChange={(checked) =>
                  handleRoundChange("rodada2", !!checked.checked)
                }
              >
                <Checkbox.HiddenInput />
                <Checkbox.Control
                  w={8}
                  h={8}
                  borderRadius="2xl"
                  color={"transparent"}
                  bgColor={
                    selectedRounds.has("rodada2") ? "#a6864a" : "gray.600"
                  }
                />
                <Checkbox.Label fontSize="xl">Rodada 2</Checkbox.Label>
              </Checkbox.Root>
            </VStack>
          </VStack>

          {/* Right Content - Segmentos lado a lado */}
          <Flex
            flex={1}
            gap={16}
            alignItems={"center"}
            flexDirection={"column"}
            overflowX="auto"
          >
            <Text fontSize="30px" color="white" alignSelf={"center"}>
              Escolha o Segmento e Cargo
            </Text>
            <HStack align="flex-start" gap={8} minW="fit-content">
              {data.data.map((segment) => (
                <VStack key={segment.secureId} align="stretch" minW="400px">
                  <HierarchicalCheckboxCard
                    id={segment.secureId}
                    label={segment.segmento_nome}
                    isChecked={selectedItems.has(segment.secureId)}
                    onChange={handleItemChange}
                    level="segment"
                  >
                    {segment.superintendencias.map((superintendencia) => (
                      <HierarchicalCheckboxCard
                        key={superintendencia.secureId}
                        id={superintendencia.secureId}
                        label={superintendencia.superintendencia_nome}
                        isChecked={selectedItems.has(superintendencia.secureId)}
                        onChange={handleItemChange}
                        level="superintendencia"
                      >
                        {superintendencia.gerencias.map((gerencia) => (
                          <HierarchicalCheckboxCard
                            key={gerencia.secureId}
                            id={gerencia.secureId}
                            label={gerencia.gerencia_nome}
                            isChecked={selectedItems.has(gerencia.secureId)}
                            onChange={handleItemChange}
                            level="gerencia"
                          >
                            {gerencia.cargos.map((cargo) => (
                              <HierarchicalCheckboxCard
                                key={cargo.secureId}
                                id={cargo.secureId}
                                label={cargo.cargo_nome}
                                isChecked={selectedItems.has(cargo.secureId)}
                                onChange={handleItemChange}
                                level="cargo"
                              />
                            ))}
                          </HierarchicalCheckboxCard>
                        ))}
                      </HierarchicalCheckboxCard>
                    ))}
                  </HierarchicalCheckboxCard>
                </VStack>
              ))}
            </HStack>
          </Flex>
        </HStack>
      </VStack>
    </Flex>
  );
}
