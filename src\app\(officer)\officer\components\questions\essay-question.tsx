import Button from "@/components/global/buttons/button";
import { Flex, HStack, Textarea, VStack, Text, Box } from "@chakra-ui/react";
import { useEffect, useState } from "react";
type EssayQuestionProps = {
  handleChangeForObjective: (
    value: string,
    type: "essay" | "objective"
  ) => void;
  savedValue?: string;
  questionStatement: string;
};
export default function EssayQuestion({
  handleChangeForObjective,
  savedValue = "",
  questionStatement,
}: EssayQuestionProps) {
  const [answer, setAnswers] = useState<string>("");
  const handleConfirm = () => {
    console.log("Resposta confirmada:", answer);
    handleChangeForObjective(answer, "essay");
  };
  useEffect(() => {
    if (savedValue) {
      setAnswers(savedValue);
    }
  }, [savedValue]);
  return (
    <>
      <VStack
        w={"100%"}
        h={"100%"}
        justifyContent="center"
        alignItems="start"
        gap={8}
      >
        <Text
          fontSize={{ base: 16, "2xl": 26 }}
          fontWeight="bold"
          color="black"
        >
          {questionStatement}
        </Text>
        <Box w={"100%"} h={"60%"} display="flex" justifyContent="center">
          <Textarea
            w={"100%"}
            h={"100%"}
            borderTop={"20px solid rgb(32,32,31)"}
            borderBottom={"20px solid rgb(32,32,31)"}
            borderTopRadius={20}
            borderBottomRadius={20}
            bgColor={"rgb(241,241,241)"}
            resize="none"
            color="black"
            p={4}
            _scrollbar={{
              backgroundColor: "rgb(225,223,224)",
            }}
            _scrollbarThumb={{
              backgroundColor: "rgb(198,198,198)",
            }}
            placeholder="Digite sua resposta..."
            value={answer}
            onChange={(e) => {
              setAnswers(e.target.value);
            }}
          />
        </Box>
        <Flex width={"100%"} justifyContent="flex-end">
          <Button
            bgColor={"rgb(103, 42, 32)"}
            borderRadius={20}
            px={8}
            py={4}
            onClick={handleConfirm}
          >
            <Text fontSize={{ base: 18, "2xl": 22 }} fontWeight={"bold"}>
              Confirmar resposta
            </Text>
          </Button>
        </Flex>
      </VStack>
    </>
  );
}
