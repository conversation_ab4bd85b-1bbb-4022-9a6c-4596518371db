import Button from "@/components/global/buttons/button";
import DefaultButton from "@/components/global/buttons/button";
import { HStack, Image, Text, VStack } from "@chakra-ui/react";

type PreStartScreenProps = {
  onButtonClick?: () => void;
};

export default function PreStartScreen({ onButtonClick }: PreStartScreenProps) {
  return (
    <HStack
      flex={1}
      h={"100%"}
      justifyContent="center"
      alignItems="center"
      bgColor={"rgb(36,35,35)"}
      gap={0}
      position={"relative"}
    >
      <Image
        src="/images/logoBancoABC.svg"
        alt="Banco ABC"
        w={"100px"}
        h="auto"
        position={"absolute"}
        top={{
          "2xl": 20,
          base: 10,
        }}
        left={{ "2xl": 20, base: 20 }}
      />
      <VStack w={"50%"} h={"100%"} justify={"center"} px={20} pt={15}>
        <VStack gap={{ "2xl": 10, base: 5 }} alignItems="start">
          <Text
            fontSize={{ "2xl": 32, base: 24 }}
            fontWeight="light"
            color={"white"}
          >
            Aqui, você encontra um verdadeiro mapa estratégico para o seu
            crescimento profissional.
          </Text>
          <Text
            fontSize={{ "2xl": 24, base: 16 }}
            fontWeight="medium"
            color={"white"}
          >
            Você está prestes a iniciar um assessment cuidadosamente projetado
            para mapear suas competências e impulsionar seu crescimento. Serão
            30 questões situacionais, desenvolvidas para refletir os desafios
            reais do seu dia a dia como Gerente de Relacionamento.
          </Text>
          <Text
            fontSize={{ "2xl": 24, base: 16 }}
            fontWeight="medium"
            color={"white"}
          >
            Reserve cerca de 1 hora para responder às perguntas com calma e
            atenção. Comece agora sua jornada de evolução.
          </Text>
          <Button p={6} px={8} borderRadius={20} onClick={onButtonClick}>
            <Text fontSize={{ "2xl": 24, base: 20 }} fontWeight={"bold"}>
              Começar
            </Text>
          </Button>
        </VStack>
      </VStack>
      <VStack
        w={"50%"}
        h={"100%"}
        justify={"center"}
        align={"center"}
        gap={8}
        pt={15}
      >
        <Text
          fontSize={{ "2xl": 32, base: 24 }}
          fontWeight="light"
          color={"white"}
        >
          O que você pode esperar ao final?
        </Text>
        <HStack
          w={"100%"}
          justifyContent="space-around"
          px={{
            "2xl": 20,
            base: 10,
          }}
          gap={5}
        >
          <VStack w={"50%"} alignItems="start">
            <Image
              src="/images/img-video-1.png"
              alt="Banco ABC"
              w="100%"
              h="auto"
              borderRadius={20}
            />
            <Text>
              Descubra seus pontos fortes e identifique áreas de desenvolvimento
              com clareza.
            </Text>
          </VStack>
          <VStack w={"50%"} alignItems="start">
            <Image
              src="/images/img-video-2.png"
              alt="Banco ABC"
              w="100%"
              h="auto"
              borderRadius={20}
            />
            <Text>
              Receba recomendações práticas de aprendizado, conectadas ao
              portfólio da Escola do ABC BRASIL.
            </Text>
          </VStack>
        </HStack>
      </VStack>
    </HStack>
  );
}
