import Button from "@/components/global/buttons/button";
import { VStack, Text, Box, Image } from "@chakra-ui/react";

type VideoScreenProps = {
  title: string;
  videoUrl: string;
  onNext: () => void;
};

export default function VideoScreen({
  title,
  videoUrl,
  onNext,
}: VideoScreenProps) {
  return (
    <VStack
      flex={1}
      h={"100%"}
      justifyContent="center"
      alignItems="center"
      position={"relative"}
      gap={0}
      zIndex={1}
      bgColor={"rgb(36,35,35)"}
    >
      <Box
        position="absolute"
        top={0}
        right={0}
        // transform="translateY(50%)"
        transform={"scale(2.5)"}
        w={"10%"}
        h={"100%"}
        bgImg="url(/images/padraoBG-01.svg)"
        bgRepeat="no-repeat"
        bgPos="center left"
        bgSize="cover"
        clipPath="inset(0 0 0 0)"
      />
      <Image
        src="/images/logoBancoABC.svg"
        alt="Banco ABC"
        w="100px"
        h="auto"
        position={"absolute"}
        top={{
          "2xl": 20,
          base: 10,
        }}
        left={{ "2xl": 20, base: 10 }}
      />
      <VStack width={"50%"} h={"100%"} justify={"center"}>
        <Text fontSize={{ base: 22, "2xl": 26 }} color={"white"} mb={8}>
          {title}
        </Text>
        <iframe
          src={videoUrl}
          height="50%"
          width="100%"
          title="YouTube video player"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          style={{ borderRadius: "30px", border: "none" }}
        />
        <Box width="100%" display={"flex"} justifyContent="flex-end" mt={3}>
          <Button px={8} borderRadius={14} onClick={onNext}>
            <Text fontSize={{ "2xl": 20, base: 16 }} fontWeight={"bold"}>
              Avançar
            </Text>
          </Button>
        </Box>
      </VStack>
    </VStack>
  );
}
