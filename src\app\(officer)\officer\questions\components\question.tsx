"use client";
import { ApiQuestionsDTO } from "@/utils/types/DTO/questions.dto";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import QuestionScreen from "../../components/questions/question-screen";
import VideoScreen from "../../components/questions/video-screen";
import { api } from "@/services/api";
import { useMutation } from "@tanstack/react-query";
import LoadingScreen from "./loading-screen";

export default function Question({
  questionData,
}: {
  questionData: ApiQuestionsDTO[];
}) {
  const router = useRouter();
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [currentQuestionGroupIndex, setCurrentQuestionGroupIndex] = useState(0);
  const [view, setView] = useState<"video" | "question">("video");
  const [isSending, setIsSending] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    const savedProgress = localStorage.getItem("assmt_progress");
    if (savedProgress) {
      const { videoIndex, questionGroupIndex, currentView, time } =
        JSON.parse(savedProgress);
      setCurrentVideoIndex(videoIndex);
      setCurrentQuestionGroupIndex(questionGroupIndex);
      setView(currentView);
      if (time) {
        setElapsedTime(time);
      }
    }
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (isInitialized) {
      const progress = {
        videoIndex: currentVideoIndex,
        questionGroupIndex: currentQuestionGroupIndex,
        currentView: view,
        time: elapsedTime,
      };
      localStorage.setItem("assmt_progress", JSON.stringify(progress));
    }
  }, [
    currentVideoIndex,
    currentQuestionGroupIndex,
    view,
    isInitialized,
    elapsedTime,
  ]);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval> | undefined;

    const startTimer = () => {
      interval = setInterval(() => {
        setElapsedTime((prevTime) => prevTime + 1);
      }, 1000);
    };

    const stopTimer = () => {
      if (interval) {
        clearInterval(interval);
        interval = undefined;
      }
    };

    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopTimer();
      } else if (view === "question") {
        startTimer();
      }
    };

    if (view === "question" && !document.hidden) {
      startTimer();
    }

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      stopTimer();
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [view]);

  const currentVideo = questionData[currentVideoIndex];
  const currentQuestionGroup =
    currentVideo?.questionsGroups[currentQuestionGroupIndex];

  const totalQuestionGroups = questionData.reduce(
    (total, video) => total + video.questionsGroups.length,
    0
  );

  const globalQuestionGroupIndex =
    questionData
      .slice(0, currentVideoIndex)
      .reduce((total, video) => total + video.questionsGroups.length, 0) +
    currentQuestionGroupIndex;

  useEffect(() => {
    setIsSending(false);
  }, [setIsSending]);

  const handleNext = () => {
    if (view === "video") {
      setView("question");
    } else {
      const isLastQuestionGroup =
        currentQuestionGroupIndex === currentVideo.questionsGroups.length - 1;
      const isLastVideo = currentVideoIndex === questionData.length - 1;

      if (isLastQuestionGroup) {
        if (isLastVideo) {
          setIsSending(true);
          sendAnswer();
        } else {
          setCurrentVideoIndex(currentVideoIndex + 1);
          setCurrentQuestionGroupIndex(0);
          setView("video");
        }
      } else {
        setCurrentQuestionGroupIndex(currentQuestionGroupIndex + 1);
      }
    }
  };

  const sendApiMutate = useMutation({
    mutationFn: async (data: any) =>
      await api.post("/officer/answer/checkin", {
        timer: elapsedTime,
        ...data,
      }),
    onSuccess: () => {
      localStorage.removeItem("assmt_answers");
      localStorage.removeItem("assmt_progress");
      router.push("/officer/conclusion");
    },
    onError: (error) => {
      console.error("Error sending answers:", error);
    },
  });

  const sendAnswer = () => {
    const savedAnswers = localStorage.getItem("assmt_answers");
    if (savedAnswers) {
      const savedObject = JSON.parse(savedAnswers);
      sendApiMutate.mutate(savedObject);
      return;
    }
  };

  if (!isInitialized) {
    return <LoadingScreen />;
  }

  if (!currentVideo) {
    router.push("/officer");
    return null;
  }

  if (view === "video") {
    return (
      <VideoScreen
        videoUrl={currentVideo.url}
        title={currentVideo.title}
        onNext={handleNext}
      />
    );
  }

  if (isSending || sendApiMutate.isPending) {
    return <LoadingScreen />;
  }

  if (view === "question" && currentQuestionGroup && !isSending) {
    return (
      <QuestionScreen
        questionGroup={currentQuestionGroup}
        questionGroupIndex={globalQuestionGroupIndex}
        questionGroupsTotal={totalQuestionGroups}
        onConfirm={handleNext}
        elapsedTime={elapsedTime}
        onWatchVideoAgain={() => setView("video")}
      />
    );
  }

  return null;
}
