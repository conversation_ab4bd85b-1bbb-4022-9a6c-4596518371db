"use client";
import { useState, useEffect } from "react";
import Button from "@/components/global/buttons/button";
import { useAuthContext } from "@/providers/auth-provider";
import { Box, HStack, Text, VStack, Image } from "@chakra-ui/react";
import DefaultScreen from "./components/default-screen";
import PreStartScreen from "./components/pre-start-screen";
import VideoScreen from "./components/questions/video-screen";
import QuestionScreen from "./components/questions/question-screen";
import { useRouter } from "next/navigation";

export default function Page() {
  const { user } = useAuthContext();
  const router = useRouter();
  const [currentScreen, setCurrentScreen] = useState(0);

  // Reset para a primeira tela quando o usuário logar ou entrar no site
  useEffect(() => {
    setCurrentScreen(0);
  }, [user]);

  const nextScreen = () => {
    if (currentScreen < 2) {
      setCurrentScreen(currentScreen + 1);
    } else if (currentScreen === 2) {
      router.push("/officer/questions");
    }
  };

  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 0:
        return (
          <DefaultScreen
            topText="Este é o primeiro passo para potencializar suas competências e alinhá-las à ambição do ABC BRASIL."
            bottomText={
              <>
                A plataforma de assessment da Escola de Excelência Comercial foi
                cuidadosamente desenvolvida para refletir os desafios do nosso
                mercado e ajudá-lo a identificar oportunidades que impulsionem
                sua carreira e gerem impacto significativo nos resultados.
                <br />
                <br />
                Assista ao vídeo e depois clique em "Continuar" para iniciar sua
                jornada de autoavaliação.
              </>
            }
            buttonText="Continuar"
            videoSrc=""
            userName={user?.name}
            isInverted={false}
            onButtonClick={nextScreen}
          />
        );
      case 1:
        return (
          <DefaultScreen
            topText="No ABC BRASIL, acreditamos que a evolução começa com uma análise estratégica"
            bottomText={
              <>
                Este assessment vai além de medir competências; ele é uma
                ferramenta que conecta suas habilidades às metas corporativas,
                contribuindo diretamente para o sucesso de nossos clientes e do
                banco. Clique no vídeo e confira o que nosso VP tem a dizer.
                <br />
                <br />
                Pronto para começar?
                <br />
                Siga em frente e descubra como o processo foi desenvolvido
                especialmente para você.
              </>
            }
            buttonText="Continuar"
            videoSrc=""
            isInverted={true}
            onButtonClick={nextScreen}
          />
        );
      case 2:
        return <PreStartScreen onButtonClick={nextScreen} />;
      default:
        return null;
    }
  };

  return <Box height="100vh">{renderCurrentScreen()}</Box>;
}
