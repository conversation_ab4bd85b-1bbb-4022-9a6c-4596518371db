import Button from "@/components/global/buttons/button";
import { VStack, Text, RadioGroup, Flex, HStack, Box } from "@chakra-ui/react";
import { useEffect, useState } from "react";

const returnLabelByOrder = (order: number) => {
  switch (order) {
    case 0:
      return "A";
    case 1:
      return "B";
    case 2:
      return "C";
    case 3:
      return "D";
    case 4:
      return "E";
    default:
      return "";
  }
};

type ObjectiveQuestionProps = {
  handleSaveAnswer?: (value: string, type: "essay" | "objective") => void;
  options: {
    secureId: string;
    description: string;
    order: number;
  }[];
  savedValue?: string | number;
  questionStatement: string;
};

export default function ObjectiveQuestion({
  handleSaveAnswer,
  options,
  savedValue,
  questionStatement,
}: ObjectiveQuestionProps) {
  const [selectedValue, setSelectedValue] = useState<number>();

  const handleOptionChange = (value: number) => {
    setSelectedValue(value);
  };

  useEffect(() => {
    if (savedValue !== undefined) {
      const savedOption = options.find(
        (option) => option.secureId === savedValue
      );
      if (savedOption) {
        setSelectedValue(savedOption.order);
      } else {
        setSelectedValue(undefined); // Reset if saved value is not found
      }
    }
  }, [savedValue]);

  const handleConfirm = () => {
    if (selectedValue !== undefined) {
      const selectedOption = options.find(
        (option) => option.order === selectedValue
      );
      if (selectedOption) {
        handleSaveAnswer?.(selectedOption.secureId, "objective");
      }
      setSelectedValue(undefined); // Reset selected value after confirmation
    }
  };

  return (
    <>
      <VStack
        w={{ "2xl": "100%", base: "100%" }}
        h={"100%"}
        justifyContent="center"
        alignItems="start"
        gap={{
          base: 4,
          "2xl": 8,
        }}
      >
        <Text
          fontSize={{ base: 16, "2xl": 26 }}
          fontWeight="bold"
          color="black"
          textAlign={"justify"}
        >
          {questionStatement}
        </Text>
        <VStack w={"100%"} gap={4}>
          {options?.map((option) => {
            return (
              <HStack
                w={"100%"}
                gap={{
                  base: 2,
                  "2xl": 4,
                }}
                border={"1px solid black"}
                rounded="full"
                p={2}
                key={option.secureId}
                onClick={() => handleOptionChange(option.order)}
                cursor="pointer"
                _hover={{ bgColor: "gray.100" }}
              >
                <Box
                  border={{
                    base: "1px solid black",
                    "2xl": "2px solid black",
                  }}
                  bgColor={
                    selectedValue === option.order
                      ? "rgb(103, 42, 32)"
                      : "white"
                  }
                  width={{ base: "30px", "2xl": "50px" }}
                  height={{ base: "30px", "2xl": "50px" }}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  flexShrink={0}
                  rounded="full"
                >
                  <Text
                    color={selectedValue === option.order ? "white" : "black"}
                    fontSize={{ base: "sm", "2xl": "2xl" }}
                  >
                    {returnLabelByOrder(option.order)}
                  </Text>
                </Box>
                <Text
                  color={"black"}
                  fontSize={{ "2xl": "md", base: "xs" }}
                  textAlign={"justify"}
                  pr={{ base: 2, "2xl": 4 }}
                >
                  {option.description}
                </Text>
              </HStack>
            );
          })}
        </VStack>
        <Flex width={"100%"} justifyContent="flex-end">
          <Button
            bgColor={"rgb(103, 42, 32)"}
            borderRadius={20}
            px={8}
            py={4}
            onClick={handleConfirm}
          >
            <Text fontSize={{ base: 18, "2xl": 22 }} fontWeight={"bold"}>
              Confirmar resposta
            </Text>
          </Button>
        </Flex>
      </VStack>
    </>
  );
}
