"use client";
import Button from "@/components/global/buttons/button";
import { Box, HStack, Text, VStack, Image } from "@chakra-ui/react";

type DefaultScreenProps = {
  userName?: string;
  topText: React.ReactNode;
  videoSrc: string;
  bottomText: React.ReactNode;
  buttonText: string;
  isInverted?: boolean;
  onButtonClick?: () => void;
};

export default function DefaultScreen({
  bottomText,
  buttonText,
  topText,
  videoSrc,
  isInverted,
  userName,
  onButtonClick,
}: DefaultScreenProps) {
  return (
    <HStack
      flex={1}
      h={"100%"}
      justifyContent="center"
      alignItems="center"
      gap={0}
    >
      <VStack w={"50%"} h={"100%"}>
        <VStack
          flex={1}
          bgColor={isInverted ? "rgb(35,34,34)" : "rgb(255,255,255)"}
        >
          <VStack
            h={"50%"}
            w={"100%"}
            justifyContent="start"
            alignItems="center"
            px={20}
          >
            <HStack
              w={"100%"}
              h={"50%"}
              justifyContent={"start"}
              alignItems="center"
              gap={10}
            >
              <Image
                src="/images/logoBancoABC.svg"
                alt="Banco ABC"
                w="100px"
                h="auto"
                filter={
                  isInverted
                    ? ""
                    : "brightness(0) saturate(100%) invert(11%) sepia(4%) saturate(0%) hue-rotate(314deg) brightness(96%) contrast(88%)"
                }
              />
              {userName && (
                <Text
                  color={isInverted ? "white" : "rgb(32,32,31)"}
                  fontSize={"3xl"}
                  fontWeight="bold"
                >
                  Bem-Vindo, <br /> {userName}.
                </Text>
              )}
            </HStack>
            <Text
              fontSize={{ "2xl": 32, base: 26 }}
              fontWeight="light"
              color={isInverted ? "white" : "rgb(32,32,31)"}
            >
              {topText}
            </Text>
          </VStack>
          <VStack
            h={"50%"}
            bgColor={isInverted ? "rgb(255,255,255)" : "rgb(35,34,34)"}
            borderTopEndRadius={60}
            p={20}
            gap={8}
            justify={"center"}
            align="start"
          >
            <Text
              fontSize={{ "2xl": 24, base: 16 }}
              fontWeight="medium"
              color={isInverted ? "rgb(35,34,34)" : "white"}
            >
              {bottomText}
            </Text>
            <Button
              w={{ "2xl": 40, base: 32 }}
              h={{ "2xl": 14, base: 12 }}
              borderRadius={20}
              fontSize={{ "2xl": 24, base: 16 }}
              fontWeight={"extrabold"}
              onClick={onButtonClick}
            >
              {buttonText}
            </Button>
          </VStack>
        </VStack>
      </VStack>
      <Box
        w={"50%"}
        h={"100%"}
        bgColor={isInverted ? "rgb(35,34,34)" : "rgb(255,255,255)"}
      >
        <Box
          w={"100%"}
          h={"100%"}
          borderTopLeftRadius={60}
          bgColor={isInverted ? "rgb(255,255,255)" : "rgb(35,34,34)"}
          overflow="hidden"
        >
          <iframe
            src={videoSrc}
            width="100%"
            height="100%"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title="Video Assessment"
            style={{
              borderRadius: "60px 0 0 60px",
              border: "transparent",
            }}
          />
        </Box>
      </Box>
    </HStack>
  );
}
